import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

/// Icon mappings between Material Design and Cupertino icons
/// This class provides a centralized mapping for common icons used throughout the app
class IconMappings {
  /// Map of common icon names to their Material and Cupertino equivalents
  static const Map<String, IconMapping> _iconMap = {
    // Navigation icons
    'home': IconMapping(
      material: Icons.home,
      cupertino: CupertinoIcons.home,
    ),
    'back': IconMapping(
      material: Icons.arrow_back,
      cupertino: CupertinoIcons.back,
    ),
    'forward': IconMapping(
      material: Icons.arrow_forward,
      cupertino: CupertinoIcons.forward,
    ),
    'close': IconMapping(
      material: Icons.close,
      cupertino: CupertinoIcons.xmark,
    ),
    'menu': IconMapping(
      material: Icons.menu,
      cupertino: CupertinoIcons.bars,
    ),
    
    // Action icons
    'add': IconMapping(
      material: Icons.add,
      cupertino: CupertinoIcons.add,
    ),
    'remove': IconMapping(
      material: Icons.remove,
      cupertino: CupertinoIcons.minus,
    ),
    'delete': IconMapping(
      material: Icons.delete,
      cupertino: CupertinoIcons.delete,
    ),
    'edit': IconMapping(
      material: Icons.edit,
      cupertino: CupertinoIcons.pencil,
    ),
    'save': IconMapping(
      material: Icons.save,
      cupertino: CupertinoIcons.floppy_disk,
    ),
    'share': IconMapping(
      material: Icons.share,
      cupertino: CupertinoIcons.share,
    ),
    'download': IconMapping(
      material: Icons.download,
      cupertino: CupertinoIcons.cloud_download,
    ),
    'upload': IconMapping(
      material: Icons.upload,
      cupertino: CupertinoIcons.cloud_upload,
    ),
    
    // Visibility icons
    'visibility': IconMapping(
      material: Icons.visibility,
      cupertino: CupertinoIcons.eye,
    ),
    'visibility_off': IconMapping(
      material: Icons.visibility_off,
      cupertino: CupertinoIcons.eye_slash,
    ),
    
    // Communication icons
    'phone': IconMapping(
      material: Icons.phone,
      cupertino: CupertinoIcons.phone,
    ),
    'email': IconMapping(
      material: Icons.email,
      cupertino: CupertinoIcons.mail,
    ),
    'message': IconMapping(
      material: Icons.message,
      cupertino: CupertinoIcons.chat_bubble,
    ),
    
    // User icons
    'person': IconMapping(
      material: Icons.person,
      cupertino: CupertinoIcons.person,
    ),
    'person_add': IconMapping(
      material: Icons.person_add,
      cupertino: CupertinoIcons.person_add,
    ),
    'group': IconMapping(
      material: Icons.group,
      cupertino: CupertinoIcons.group,
    ),
    
    // Settings icons
    'settings': IconMapping(
      material: Icons.settings,
      cupertino: CupertinoIcons.settings,
    ),
    'info': IconMapping(
      material: Icons.info,
      cupertino: CupertinoIcons.info,
    ),
    'help': IconMapping(
      material: Icons.help,
      cupertino: CupertinoIcons.question_circle,
    ),
    
    // Media icons
    'play': IconMapping(
      material: Icons.play_arrow,
      cupertino: CupertinoIcons.play,
    ),
    'pause': IconMapping(
      material: Icons.pause,
      cupertino: CupertinoIcons.pause,
    ),
    'stop': IconMapping(
      material: Icons.stop,
      cupertino: CupertinoIcons.stop,
    ),
    'volume_up': IconMapping(
      material: Icons.volume_up,
      cupertino: CupertinoIcons.volume_up,
    ),
    'volume_off': IconMapping(
      material: Icons.volume_off,
      cupertino: CupertinoIcons.volume_off,
    ),
    
    // Status icons
    'check': IconMapping(
      material: Icons.check,
      cupertino: CupertinoIcons.check_mark,
    ),
    'error': IconMapping(
      material: Icons.error,
      cupertino: CupertinoIcons.exclamationmark_circle,
    ),
    'warning': IconMapping(
      material: Icons.warning,
      cupertino: CupertinoIcons.exclamationmark_triangle,
    ),
    'success': IconMapping(
      material: Icons.check_circle,
      cupertino: CupertinoIcons.check_mark_circled,
    ),
    
    // Search and filter
    'search': IconMapping(
      material: Icons.search,
      cupertino: CupertinoIcons.search,
    ),
    'filter': IconMapping(
      material: Icons.filter_list,
      cupertino: CupertinoIcons.line_horizontal_3_decrease,
    ),
    'sort': IconMapping(
      material: Icons.sort,
      cupertino: CupertinoIcons.sort_up,
    ),
    
    // Location icons
    'location': IconMapping(
      material: Icons.location_on,
      cupertino: CupertinoIcons.location,
    ),
    'location_off': IconMapping(
      material: Icons.location_off,
      cupertino: CupertinoIcons.location_slash,
    ),
    
    // Time icons
    'time': IconMapping(
      material: Icons.access_time,
      cupertino: CupertinoIcons.time,
    ),
    'calendar': IconMapping(
      material: Icons.calendar_today,
      cupertino: CupertinoIcons.calendar,
    ),
    
    // Shopping icons
    'shopping_cart': IconMapping(
      material: Icons.shopping_cart,
      cupertino: CupertinoIcons.shopping_cart,
    ),
    'favorite': IconMapping(
      material: Icons.favorite,
      cupertino: CupertinoIcons.heart,
    ),
    'favorite_border': IconMapping(
      material: Icons.favorite_border,
      cupertino: CupertinoIcons.heart,
    ),
    
    // Notification icons
    'notifications': IconMapping(
      material: Icons.notifications,
      cupertino: CupertinoIcons.bell,
    ),
    'notifications_off': IconMapping(
      material: Icons.notifications_off,
      cupertino: CupertinoIcons.bell_slash,
    ),
  };

  /// Get the appropriate icon data for the current platform
  static IconData? getIcon(String iconName, {bool forceMaterial = false, bool forceCupertino = false}) {
    final mapping = _iconMap[iconName];
    if (mapping == null) return null;

    if (forceMaterial) return mapping.material;
    if (forceCupertino) return mapping.cupertino;

    // Auto-detect platform
    return mapping.getForCurrentPlatform();
  }

  /// Get all available icon names
  static List<String> get availableIcons => _iconMap.keys.toList();

  /// Check if an icon name is available
  static bool hasIcon(String iconName) => _iconMap.containsKey(iconName);
}

/// Data class to hold Material and Cupertino icon mappings
class IconMapping {
  final IconData material;
  final IconData cupertino;

  const IconMapping({
    required this.material,
    required this.cupertino,
  });

  /// Get the appropriate icon for the current platform
  IconData getForCurrentPlatform() {
    // Import dart:io to check platform
    try {
      // Use Theme.of(context).platform if available, otherwise default to material
      return material; // Default fallback
    } catch (e) {
      return material;
    }
  }
}
