import 'package:flutter/material.dart';
import 'platform_icon.dart';
import '../constants/app_colors.dart';

/// Example screen demonstrating the usage of PlatformIcon widget
/// This file shows various ways to use the PlatformIcon widget throughout the app
class PlatformIconExampleScreen extends StatelessWidget {
  const PlatformIconExampleScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Platform Icon Examples'),
        leading: const PlatformIcon(
          iconName: 'back',
          color: Colors.white,
        ),
        actions: [
          IconButton(
            onPressed: () {},
            icon: const PlatformIcon(
              iconName: 'search',
              color: Colors.white,
            ),
          ),
          IconButton(
            onPressed: () {},
            icon: const PlatformIcon(
              iconName: 'menu',
              color: Colors.white,
            ),
          ),
        ],
        backgroundColor: AppColors.primaryColor,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              'Basic Usage',
              [
                _buildIconRow('Home', 'home'),
                _buildIconRow('Person', 'person'),
                _buildIconRow('Settings', 'settings'),
                _buildIconRow('Search', 'search'),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              'Action Icons',
              [
                _buildIconRow('Add', 'add'),
                _buildIconRow('Edit', 'edit'),
                _buildIconRow('Delete', 'delete'),
                _buildIconRow('Share', 'share'),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              'Status Icons',
              [
                _buildIconRow('Success', 'success'),
                _buildIconRow('Error', 'error'),
                _buildIconRow('Warning', 'warning'),
                _buildIconRow('Info', 'info'),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              'Colored Icons',
              [
                _buildColoredIconRow('Primary Color', 'favorite', AppColors.primaryColor),
                _buildColoredIconRow('Secondary Text', 'message', AppColors.textSecondary),
                _buildColoredIconRow('Grey Color', 'time', AppColors.greyColor),
                _buildColoredIconRow('Error Color', 'error', AppColors.errorColor),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              'Different Sizes',
              [
                _buildSizedIconRow('Small (16px)', 'star', 16.0),
                _buildSizedIconRow('Medium (24px)', 'star', 24.0),
                _buildSizedIconRow('Large (32px)', 'star', 32.0),
                _buildSizedIconRow('Extra Large (48px)', 'star', 48.0),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              'Predefined Color Variants',
              [
                _buildPresetIconRow('Primary', 'favorite'),
                _buildPresetIconRow('Secondary', 'message'),
                _buildPresetIconRow('Grey', 'time'),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              'String Extension Usage',
              [
                _buildExtensionIconRow('Home Extension', 'home'),
                _buildExtensionIconRow('Search Extension', 'search'),
                _buildExtensionIconRow('Settings Extension', 'settings'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildIconRow(String label, String iconName) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          PlatformIcon(
            iconName: iconName,
            size: 24.0,
            color: AppColors.textPrimary,
          ),
          const SizedBox(width: 16),
          Text(
            label,
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildColoredIconRow(String label, String iconName, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          PlatformIcon(
            iconName: iconName,
            size: 24.0,
            color: color,
          ),
          const SizedBox(width: 16),
          Text(
            label,
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildSizedIconRow(String label, String iconName, double size) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          PlatformIcon(
            iconName: iconName,
            size: size,
            color: AppColors.primaryColor,
          ),
          const SizedBox(width: 16),
          Text(
            label,
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildPresetIconRow(String label, String iconName) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          if (label == 'Primary')
            PlatformIcon.primary(iconName: iconName, size: 24.0)
          else if (label == 'Secondary')
            PlatformIcon.secondary(iconName: iconName, size: 24.0)
          else if (label == 'Grey')
            PlatformIcon.grey(iconName: iconName, size: 24.0),
          const SizedBox(width: 16),
          Text(
            '$label Preset',
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildExtensionIconRow(String label, String iconName) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          iconName.toPlatformIcon(
            size: 24.0,
            color: AppColors.primaryColor,
          ),
          const SizedBox(width: 16),
          Text(
            label,
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }
}
