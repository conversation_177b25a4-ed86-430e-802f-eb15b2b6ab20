import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:io' show Platform;
import 'icon_mappings.dart';
import '../constants/app_colors.dart';

/// A custom icon widget that automatically uses Material Icons for Android
/// and Cupertino Icons for iOS, providing a consistent cross-platform experience.
///
/// This widget serves as a replacement for the standard Icon widget throughout
/// the app, ensuring platform-appropriate icons are displayed.
///
/// Example usage:
/// ```dart
/// PlatformIcon(
///   iconName: 'home',
///   size: 24.0,
///   color: AppColors.primaryColor,
/// )
/// ```
///
/// For direct icon data usage:
/// ```dart
/// PlatformIcon.fromIconData(
///   Icons.home, // Will be automatically converted on iOS
///   size: 24.0,
/// )
/// ```
class PlatformIcon extends StatelessWidget {
  /// The name of the icon from the predefined icon mappings
  final String? iconName;

  /// Direct icon data (will be platform-converted if possible)
  final IconData? iconData;

  /// The size of the icon in logical pixels
  final double? size;

  /// The color of the icon
  final Color? color;

  /// Semantic label for accessibility
  final String? semanticLabel;

  /// Text direction for the icon
  final TextDirection? textDirection;

  /// Whether to force Material Design icons regardless of platform
  final bool forceMaterial;

  /// Whether to force Cupertino icons regardless of platform
  final bool forceCupertino;

  /// Fallback icon to use if the specified icon is not found
  final IconData? fallbackIcon;

  /// Creates a platform-aware icon using a predefined icon name
  const PlatformIcon({
    super.key,
    required this.iconName,
    this.size,
    this.color,
    this.semanticLabel,
    this.textDirection,
    this.forceMaterial = false,
    this.forceCupertino = false,
    this.fallbackIcon,
  }) : iconData = null;

  /// Creates a platform-aware icon from direct IconData
  const PlatformIcon.fromIconData(
    this.iconData, {
    super.key,
    this.size,
    this.color,
    this.semanticLabel,
    this.textDirection,
    this.forceMaterial = false,
    this.forceCupertino = false,
    this.fallbackIcon,
  }) : iconName = null;

  /// Creates a platform-aware icon with app's primary color
  const PlatformIcon.primary({
    super.key,
    required this.iconName,
    this.size,
    this.semanticLabel,
    this.textDirection,
    this.forceMaterial = false,
    this.forceCupertino = false,
    this.fallbackIcon,
  }) : iconData = null, color = AppColors.primaryColor;

  /// Creates a platform-aware icon with app's secondary text color
  const PlatformIcon.secondary({
    super.key,
    required this.iconName,
    this.size,
    this.semanticLabel,
    this.textDirection,
    this.forceMaterial = false,
    this.forceCupertino = false,
    this.fallbackIcon,
  }) : iconData = null, color = AppColors.textSecondary;

  /// Creates a platform-aware icon with grey color
  const PlatformIcon.grey({
    super.key,
    required this.iconName,
    this.size,
    this.semanticLabel,
    this.textDirection,
    this.forceMaterial = false,
    this.forceCupertino = false,
    this.fallbackIcon,
  }) : iconData = null, color = AppColors.greyColor;

  @override
  Widget build(BuildContext context) {
    final IconData? resolvedIcon = _resolveIcon(context);

    if (resolvedIcon == null) {
      // Return a placeholder or empty container if no icon is found
      return SizedBox(
        width: size ?? 24.0,
        height: size ?? 24.0,
        child: const Icon(Icons.help_outline),
      );
    }

    return Icon(
      resolvedIcon,
      size: size,
      color: color ?? Theme.of(context).iconTheme.color,
      semanticLabel: semanticLabel,
      textDirection: textDirection,
    );
  }

  /// Resolves the appropriate icon based on platform and configuration
  IconData? _resolveIcon(BuildContext context) {
    IconData? icon;

    // First, try to get icon from iconName
    if (iconName != null) {
      icon = IconMappings.getIcon(
        iconName!,
        forceMaterial: forceMaterial,
        forceCupertino: forceCupertino,
      );
    }

    // If no icon found from name, try direct iconData
    if (icon == null && iconData != null) {
      icon = _convertIconDataForPlatform(iconData!, context);
    }

    // Use fallback if still no icon found
    if (icon == null && fallbackIcon != null) {
      icon = _convertIconDataForPlatform(fallbackIcon!, context);
    }

    return icon;
  }

  /// Converts IconData to platform-appropriate equivalent if possible
  IconData _convertIconDataForPlatform(IconData originalIcon, BuildContext context) {
    if (forceMaterial) return originalIcon;
    if (forceCupertino) return _getCupertinoEquivalent(originalIcon) ?? originalIcon;

    // Auto-detect platform
    final bool isIOS = _isIOSPlatform(context);

    if (isIOS) {
      return _getCupertinoEquivalent(originalIcon) ?? originalIcon;
    }

    return originalIcon;
  }

  /// Determines if the current platform is iOS
  bool _isIOSPlatform(BuildContext context) {
    try {
      return Platform.isIOS;
    } catch (e) {
      // Fallback to theme platform detection for web or other platforms
      return Theme.of(context).platform == TargetPlatform.iOS;
    }
  }

  /// Gets Cupertino equivalent for common Material icons
  IconData? _getCupertinoEquivalent(IconData materialIcon) {
    // Map common Material icons to Cupertino equivalents using codePoint comparison
    final Map<int, IconData> materialToCupertino = {
      Icons.visibility.codePoint: CupertinoIcons.eye,
      Icons.visibility_off.codePoint: CupertinoIcons.eye_slash,
      Icons.home.codePoint: CupertinoIcons.home,
      Icons.person.codePoint: CupertinoIcons.person,
      Icons.settings.codePoint: CupertinoIcons.settings,
      Icons.search.codePoint: CupertinoIcons.search,
      Icons.add.codePoint: CupertinoIcons.add,
      Icons.remove.codePoint: CupertinoIcons.minus,
      Icons.close.codePoint: CupertinoIcons.xmark,
      Icons.arrow_back.codePoint: CupertinoIcons.back,
      Icons.arrow_forward.codePoint: CupertinoIcons.forward,
      Icons.menu.codePoint: CupertinoIcons.bars,
      Icons.phone.codePoint: CupertinoIcons.phone,
      Icons.email.codePoint: CupertinoIcons.mail,
      Icons.check.codePoint: CupertinoIcons.check_mark,
      Icons.error.codePoint: CupertinoIcons.exclamationmark_circle,
      Icons.warning.codePoint: CupertinoIcons.exclamationmark_triangle,
      Icons.info.codePoint: CupertinoIcons.info,
      Icons.favorite.codePoint: CupertinoIcons.heart,
      Icons.favorite_border.codePoint: CupertinoIcons.heart,
      Icons.share.codePoint: CupertinoIcons.share,
      Icons.delete.codePoint: CupertinoIcons.delete,
      Icons.edit.codePoint: CupertinoIcons.pencil,
      Icons.notifications.codePoint: CupertinoIcons.bell,
      Icons.location_on.codePoint: CupertinoIcons.location,
      Icons.access_time.codePoint: CupertinoIcons.time,
      Icons.calendar_today.codePoint: CupertinoIcons.calendar,
      Icons.shopping_cart.codePoint: CupertinoIcons.shopping_cart,
    };

    return materialToCupertino[materialIcon.codePoint];
  }
}

/// Extension to provide easy access to platform icons
extension PlatformIconExtension on String {
  /// Convert a string to a PlatformIcon widget
  PlatformIcon toPlatformIcon({
    double? size,
    Color? color,
    String? semanticLabel,
    TextDirection? textDirection,
    bool forceMaterial = false,
    bool forceCupertino = false,
    IconData? fallbackIcon,
  }) {
    return PlatformIcon(
      iconName: this,
      size: size,
      color: color,
      semanticLabel: semanticLabel,
      textDirection: textDirection,
      forceMaterial: forceMaterial,
      forceCupertino: forceCupertino,
      fallbackIcon: fallbackIcon,
    );
  }
}
