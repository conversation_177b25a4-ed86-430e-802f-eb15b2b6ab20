# PlatformIcon Widget

A custom Flutter widget that automatically uses Material Icons for Android and Cupertino Icons for iOS, providing a consistent cross-platform experience.

## Features

- 🎯 **Platform-aware**: Automatically chooses appropriate icons for iOS and Android
- 🎨 **Color integration**: Works seamlessly with your app's color scheme
- 📱 **Comprehensive mappings**: Includes mappings for 50+ common icons
- 🔧 **Flexible API**: Multiple constructors for different use cases
- 🧪 **Well-tested**: Comprehensive test coverage
- 🚀 **Easy to use**: Drop-in replacement for standard Icon widget

## Basic Usage

### Using predefined icon names

```dart
PlatformIcon(
  iconName: 'home',
  size: 24.0,
  color: Colors.blue,
)
```

### Using direct IconData

```dart
PlatformIcon.fromIconData(
  Icons.star,
  size: 28.0,
  color: Colors.yellow,
)
```

### Using predefined color variants

```dart
// Uses AppColors.primaryColor
PlatformIcon.primary(
  iconName: 'favorite',
  size: 24.0,
)

// Uses AppColors.textSecondary
PlatformIcon.secondary(
  iconName: 'message',
  size: 24.0,
)

// Uses AppColors.greyColor
PlatformIcon.grey(
  iconName: 'time',
  size: 24.0,
)
```

### Using string extension

```dart
'home'.toPlatformIcon(
  size: 30.0,
  color: Colors.red,
)
```

## Available Icon Names

The widget supports 50+ predefined icon names including:

### Navigation
- `home`, `back`, `forward`, `close`, `menu`

### Actions
- `add`, `remove`, `delete`, `edit`, `save`, `share`, `download`, `upload`

### Visibility
- `visibility`, `visibility_off`

### Communication
- `phone`, `email`, `message`

### User
- `person`, `person_add`, `group`

### Settings
- `settings`, `info`, `help`

### Media
- `play`, `pause`, `stop`, `volume_up`, `volume_off`

### Status
- `check`, `error`, `warning`, `success`

### Search & Filter
- `search`, `filter`, `sort`

### Location
- `location`, `location_off`

### Time
- `time`, `calendar`

### Shopping
- `shopping_cart`, `favorite`, `favorite_border`

### Notifications
- `notifications`, `notifications_off`

## Advanced Usage

### Force specific platform icons

```dart
// Always use Material icons
PlatformIcon(
  iconName: 'home',
  forceMaterial: true,
)

// Always use Cupertino icons
PlatformIcon(
  iconName: 'home',
  forceCupertino: true,
)
```

### With fallback icon

```dart
PlatformIcon(
  iconName: 'custom_icon', // Might not exist
  fallbackIcon: Icons.help_outline,
  size: 24.0,
)
```

### With semantic label for accessibility

```dart
PlatformIcon(
  iconName: 'home',
  semanticLabel: 'Navigate to home screen',
  size: 24.0,
)
```

## Integration with Existing Code

### Replacing standard Icons

**Before:**
```dart
Icon(
  Icons.visibility_off,
  color: Colors.grey[600],
)
```

**After:**
```dart
PlatformIcon(
  iconName: 'visibility_off',
  color: Colors.grey[600],
)
```

### In IconButton

```dart
IconButton(
  onPressed: () {},
  icon: const PlatformIcon(
    iconName: 'search',
    color: Colors.white,
  ),
)
```

### In AppBar

```dart
AppBar(
  leading: const PlatformIcon(
    iconName: 'back',
    color: Colors.white,
  ),
  actions: [
    IconButton(
      onPressed: () {},
      icon: const PlatformIcon(
        iconName: 'menu',
        color: Colors.white,
      ),
    ),
  ],
)
```

## Adding New Icons

To add new icon mappings, edit `lib/widgets/icon_mappings.dart`:

```dart
static const Map<String, IconMapping> _iconMap = {
  // ... existing mappings
  'new_icon': IconMapping(
    material: Icons.new_material_icon,
    cupertino: CupertinoIcons.new_cupertino_icon,
  ),
};
```

## Testing

Run the widget tests:

```bash
flutter test test/widgets/platform_icon_test.dart
```

## Example Screen

See `lib/widgets/platform_icon_example.dart` for a comprehensive example showing all features and usage patterns.

## Dependencies

The widget requires:
- `flutter/material.dart`
- `flutter/cupertino.dart`
- `dart:io` (for platform detection)

Make sure your `pubspec.yaml` includes:

```yaml
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8

flutter:
  uses-material-design: true
```
