import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';
import '../constants/app_colors.dart';
import '../widgets/custom_text_field.dart';

enum AuthState { splash, login, otp }

class AnimatedAuthScreen extends StatefulWidget {
  const AnimatedAuthScreen({super.key});

  @override
  State<AnimatedAuthScreen> createState() => _AnimatedAuthScreenState();
}

class _AnimatedAuthScreenState extends State<AnimatedAuthScreen>
    with TickerProviderStateMixin {
  AuthState _currentState = AuthState.splash;

  // Controllers
  late AnimationController _animationController;
  late AnimationController _fadeController;
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _otpController = TextEditingController();

  // Animations
  late Animation<Offset> _buttonSlideAnimation;
  late Animation<Offset> _loginSlideAnimation;
  late Animation<Offset> _otpSlideAnimation;
  late Animation<double> _buttonFadeAnimation;
  late Animation<double> _loginFadeAnimation;
  late Animation<double> _otpFadeAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Initialize slide animations
    _buttonSlideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0, 2), // Slide down
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _loginSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 1), // Start from bottom
      end: Offset.zero, // Move to center
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _otpSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 1), // Start from bottom
      end: Offset.zero, // Move to center
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Initialize fade animations
    _buttonFadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    _loginFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    _otpFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _fadeController.dispose();
    _phoneController.dispose();
    _otpController.dispose();
    super.dispose();
  }

  void _onGetStartedPressed() async {
    setState(() {
      _currentState = AuthState.login;
    });

    // Start fade out animation for button
    _fadeController.forward();

    // Wait a bit then start slide animations
    await Future.delayed(const Duration(milliseconds: 200));
    _animationController.forward();
  }

  void _onNextPressed() async {
    setState(() {
      _currentState = AuthState.otp;
    });

    // Reset animations for next transition
    _animationController.reset();
    _fadeController.reset();

    // Start fade out for login section
    _fadeController.forward();

    // Wait a bit then start slide animations
    await Future.delayed(const Duration(milliseconds: 200));
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _currentState == AuthState.splash
          ? Colors.white
          : AppColors.backgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            // Logo Section (Always visible)
            Expanded(
              flex: 1,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo with conditional styling
                    if (_currentState == AuthState.splash) ...[
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Image.asset(
                          'assets/images/brand_logo.png',
                          width: 60,
                          height: 60,
                        ),
                      ),
                    ] else ...[
                      Image.asset(
                        'assets/images/brand_logo.png',
                        width: 60,
                        height: 60,
                      ),
                    ],
                    const SizedBox(height: 20),
                    Image.asset('assets/images/brand_logo_text.png', width: 180),
                    if (_currentState == AuthState.splash) ...[
                      const SizedBox(height: 10),
                      Text(
                        'Your Packaging Partner',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.blackColor.withValues(alpha: 0.6),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            // Dynamic Content Section
            Expanded(
              flex: 1,
              child: Stack(
                children: [
                  // Get Started Button (Splash State)
                  if (_currentState == AuthState.splash)
                    SlideTransition(
                      position: _buttonSlideAnimation,
                      child: FadeTransition(
                        opacity: _buttonFadeAnimation,
                        child: _buildGetStartedSection(),
                      ),
                    ),

                  // Login Section
                  if (_currentState == AuthState.login)
                    SlideTransition(
                      position: _loginSlideAnimation,
                      child: FadeTransition(
                        opacity: _loginFadeAnimation,
                        child: _buildLoginSection(),
                      ),
                    ),

                  // OTP Section
                  if (_currentState == AuthState.otp)
                    SlideTransition(
                      position: _otpSlideAnimation,
                      child: FadeTransition(
                        opacity: _otpFadeAnimation,
                        child: _buildOTPSection(),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGetStartedSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 40),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: _onGetStartedPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryColor,
            padding: const EdgeInsets.symmetric(vertical: 16),
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
          ),
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'Get Started',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              SizedBox(width: 8),
              Icon(Icons.arrow_forward, color: Colors.black),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoginSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(32),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Heading
            Center(
              child: Text(
                'Login/Signup',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryColor,
                ),
              ),
            ),
            const SizedBox(height: 8),

            // Subtitle
            Center(
              child: Text(
                'Login/Signup to start managing your orders.',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ),
            const SizedBox(height: 30),

            // Phone Number label
            const Text(
              'Phone Number',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 8),

            // Phone number field
            CustomTextField(
              controller: _phoneController,
              hintText: 'Enter Your Phone Number',
              keyboardType: TextInputType.phone,
              borderColor: Colors.grey[300],
              focusedBorderColor: AppColors.primaryColor,
            ),

            const SizedBox(height: 20),

            // Next button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _onNextPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                ),
                child: const Text(
                  'Next',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppColors.blackColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOTPSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(32),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Heading
            Text(
              'Verify Your Number',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: AppColors.primaryColor,
              ),
            ),
            const SizedBox(height: 8),

            // Subtitle
            Text(
              "We've sent a 5-digit code to your phone number.",
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),

            // OTP Input (Pinput)
            Pinput(
              length: 5,
              controller: _otpController,
              defaultPinTheme: PinTheme(
                width: 56,
                height: 56,
                textStyle: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade300),
                ),
              ),
              focusedPinTheme: PinTheme(
                width: 56,
                height: 56,
                textStyle: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.primaryColor),
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Resend code text
            Center(
              child: TextButton(
                onPressed: () {
                  // Resend OTP logic here
                },
                child: Text(
                  "Didn't receive the code? Resend",
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Verify Button
            SizedBox(
              width: double.infinity,
              height: 56,
              child: ElevatedButton(
                onPressed: () {
                  // Verify logic here
                  // You can navigate to the next screen or show success
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                ),
                child: const Text(
                  'Verify',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
