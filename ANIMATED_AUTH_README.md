# Animated Authentication Screen

## Overview

This implementation replaces the traditional navigation between splash, login, and OTP screens with smooth animated transitions on a single screen. Instead of navigating between different screens, UI sections slide up and down with beautiful animations.

## Features

✨ **Smooth Animations**: Sections slide up from bottom and down when transitioning
🎯 **Single Screen**: All authentication states on one screen
🎨 **Consistent Design**: Maintains your app's color scheme and design language
📱 **Responsive**: Works on different screen sizes with scrollable content
🧪 **Well Tested**: Comprehensive test coverage

## Animation Flow

### 1. Splash State (Initial)
- Shows logo with shadow effect
- "Your Packaging Partner" subtitle
- "Get Started" button at bottom

### 2. Login State (After "Get Started" tap)
- **Animation**: <PERSON><PERSON> slides down and fades out
- **Animation**: Login section slides up from bottom
- Shows phone number input field
- "Next" button to proceed

### 3. OTP State (After "Next" tap)
- **Animation**: Login section slides down and fades out  
- **Animation**: OTP section slides up from bottom
- Shows 5-digit OTP input using Pinput
- "Verify" button to complete

## Technical Implementation

### Key Components

1. **AnimationController**: Controls the timing of animations
2. **SlideTransition**: Handles vertical sliding movements
3. **FadeTransition**: Manages opacity changes
4. **State Management**: Tracks current authentication state

### Animation Details

- **Duration**: 800ms for slide animations, 600ms for fade
- **Curve**: `Curves.easeInOut` for smooth transitions
- **Direction**: Vertical sliding (up/down)
- **Timing**: Fade starts first, then slide animation

### Code Structure

```dart
enum AuthState { splash, login, otp }

class AnimatedAuthScreen extends StatefulWidget {
  // Uses TickerProviderStateMixin for multiple animations
  // Manages three states with smooth transitions
  // Responsive layout with SingleChildScrollView
}
```

## Files Modified

1. **`lib/screens/animated_auth_screen.dart`** - New unified screen
2. **`lib/main.dart`** - Updated to use AnimatedAuthScreen
3. **`test/animated_auth_screen_test.dart`** - Comprehensive tests

## Usage

The screen automatically starts in splash state. User interactions trigger animations:

1. Tap "Get Started" → Animates to login state
2. Tap "Next" → Animates to OTP state
3. Tap "Verify" → Ready for next screen navigation

## Customization

### Animation Timing
```dart
_animationController = AnimationController(
  duration: const Duration(milliseconds: 800), // Adjust timing
  vsync: this,
);
```

### Animation Curves
```dart
curve: Curves.easeInOut, // Change to Curves.bounceIn, etc.
```

### Slide Direction
```dart
begin: const Offset(0, 1), // From bottom
end: Offset.zero,          // To center
```

## Benefits

1. **Better UX**: Smooth transitions feel more polished
2. **Performance**: Single screen reduces navigation overhead
3. **Consistency**: Maintains visual continuity
4. **Modern Feel**: Follows current mobile app design trends

## Testing

Run tests to verify functionality:
```bash
flutter test test/animated_auth_screen_test.dart
```

All tests verify:
- Initial splash state display
- Transition to login state
- Transition to OTP state
- Proper widget rendering

## Next Steps

After OTP verification, you can:
1. Navigate to main app screen
2. Show success animation
3. Trigger additional onboarding flows

The implementation is ready for production use and follows Flutter best practices for animations and state management.
